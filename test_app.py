#!/usr/bin/env python3
"""
Test script for the English Test Application
Tests the document parser and basic functionality without GUI
"""

import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from document_parser import DocumentParser
from question_model import Question, TestPart, Test, UserAnswer, TestResults

def test_document_parsing():
    """Test document parsing functionality"""
    print("=== Testing Document Parser ===")
    
    parser = DocumentParser()
    
    # Test parsing test-2.docx (since test-1.docx seems corrupted)
    print("\\nParsing test-2.docx...")
    test = parser.parse_test_file("test-2.docx")
    
    if not test:
        print("❌ Failed to parse test-2.docx")
        return False
    
    print(f"✅ Successfully parsed: {test.name}")
    print(f"   Total parts: {len(test.parts)}")
    print(f"   Total questions: {test.get_total_questions()}")
    
    # Show details for each part
    for part in test.parts:
        print(f"\\n   Part {part.number}:")
        print(f"     Questions: {part.get_question_count()}")
        question_range = part.get_question_range()
        print(f"     Range: {question_range[0]}-{question_range[1]}")
        
        # Show first question as example
        if part.questions:
            q = part.questions[0]
            print(f"     Example: Q{q.number}: {q.text[:60]}...")
            for i, choice in enumerate(q.choices[:2]):  # Show first 2 choices
                print(f"       {choice}")
    
    # Validate the test
    print("\\nValidating test structure...")
    issues = parser.validate_test(test)
    if issues:
        print(f"⚠️  Found {len(issues)} validation issues:")
        for issue in issues[:3]:  # Show first 3 issues
            print(f"     - {issue}")
        if len(issues) > 3:
            print(f"     ... and {len(issues) - 3} more")
    else:
        print("✅ Test validation passed!")
    
    return test

def test_question_model():
    """Test question model functionality"""
    print("\\n=== Testing Question Model ===")
    
    # Create a sample question
    question = Question(
        number=101,
        text="The secretary filling in at the reception desk has been performing so well that management is considering offering her a _______ position.",
        choices=["(A) durable", "(B) periodic", "(C) binding", "(D) permanent"],
        correct_answer="D",
        part=5
    )
    
    print(f"✅ Created question {question.number}")
    print(f"   Text: {question.text[:50]}...")
    print(f"   Choices: {len(question.choices)}")
    print(f"   Choice letters: {question.get_choice_letters()}")
    
    # Test user answer
    user_answer = UserAnswer(question_number=101, selected_choice="D")
    is_correct = user_answer.check_correctness(question)
    print(f"   User selected: {user_answer.selected_choice}")
    print(f"   Correct: {is_correct}")
    
    return True

def test_user_interaction_simulation():
    """Simulate a user taking a test"""
    print("\\n=== Simulating User Test Session ===")
    
    parser = DocumentParser()
    test = parser.parse_test_file("test-2.docx")
    
    if not test:
        print("❌ Cannot simulate - test parsing failed")
        return False
    
    print(f"Starting simulated test: {test.name}")
    
    # Simulate answering first 5 questions
    user_answers = []
    all_questions = test.get_all_questions()
    
    for i in range(min(5, len(all_questions))):
        question = all_questions[i]
        available_choices = question.get_choice_letters()
        
        if available_choices:
            # Simulate selecting the first available choice
            selected = available_choices[0]
            user_answers.append(UserAnswer(question.number, selected))
            print(f"   Q{question.number}: Selected ({selected})")
    
    print(f"\\nSimulated answering {len(user_answers)} questions")
    
    # Calculate results (without answer key, scores will be 0)
    results = TestResults.calculate_results(test, user_answers)
    print(f"Results:")
    print(f"   Answered: {len(results.user_answers)}/{results.total_questions}")
    print(f"   Completion: {len(results.user_answers)/results.total_questions*100:.1f}%")
    
    return True

def test_file_availability():
    """Check which test files are available"""
    print("\\n=== Checking Test File Availability ===")
    
    import os
    
    files_to_check = ["test-1.docx", "test-2.docx"]
    available_files = []
    
    for filename in files_to_check:
        if os.path.exists(filename):
            print(f"✅ {filename} - Available")
            available_files.append(filename)
        else:
            print(f"❌ {filename} - Not found")
    
    if not available_files:
        print("\\n⚠️  No test files found!")
        print("   Please ensure test-1.docx and/or test-2.docx are in the current directory")
        return False
    
    print(f"\\n✅ Found {len(available_files)} test file(s)")
    return True

def main():
    """Run all tests"""
    print("English Test Application - Test Suite")
    print("=" * 50)
    
    # Check file availability first
    if not test_file_availability():
        print("\\n❌ Cannot proceed without test files")
        return
    
    # Test document parsing
    test = test_document_parsing()
    if not test:
        print("\\n❌ Document parsing failed")
        return
    
    # Test question model
    if not test_question_model():
        print("\\n❌ Question model test failed")
        return
    
    # Test user interaction simulation
    if not test_user_interaction_simulation():
        print("\\n❌ User interaction simulation failed")
        return
    
    print("\\n" + "=" * 50)
    print("✅ All tests passed!")
    print("\\nThe application should work correctly.")
    print("Run 'python3 english_test_app.py' to start the GUI application.")

if __name__ == "__main__":
    main()
