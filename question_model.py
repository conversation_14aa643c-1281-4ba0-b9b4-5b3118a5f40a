"""
Question model classes for the English Test Application
"""

from dataclasses import dataclass
from typing import List, Optional
from enum import Enum

class QuestionType(Enum):
    MULTIPLE_CHOICE = "multiple_choice"
    TEXT_COMPLETION = "text_completion"
    READING_COMPREHENSION = "reading_comprehension"

@dataclass
class Question:
    """Represents a single test question"""
    number: int
    text: str
    choices: List[str]  # For multiple choice: ["(A) option1", "(B) option2", ...]
    correct_answer: Optional[str] = None  # The correct answer choice (e.g., "A", "B", "C", "D")
    part: int = 5  # Part number (5, 6, or 7)
    question_type: QuestionType = QuestionType.MULTIPLE_CHOICE
    context: Optional[str] = None  # For reading comprehension questions
    
    def get_choice_letter(self, choice_text: str) -> Optional[str]:
        """Extract the letter (A, B, C, D) from a choice text like '(A) option'"""
        choice_text = choice_text.strip()
        if choice_text.startswith('(') and ')' in choice_text:
            return choice_text[1:choice_text.index(')')]
        return None
    
    def get_choice_letters(self) -> List[str]:
        """Get all available choice letters for this question"""
        letters = []
        for choice in self.choices:
            letter = self.get_choice_letter(choice)
            if letter:
                letters.append(letter)
        return letters

@dataclass
class TestPart:
    """Represents a part of the test (Part 5, 6, or 7)"""
    number: int
    title: str
    directions: str
    questions: List[Question]
    
    def get_question_count(self) -> int:
        """Get the number of questions in this part"""
        return len(self.questions)
    
    def get_question_range(self) -> tuple:
        """Get the range of question numbers in this part"""
        if not self.questions:
            return (0, 0)
        return (min(q.number for q in self.questions), max(q.number for q in self.questions))

@dataclass
class Test:
    """Represents a complete test"""
    name: str
    parts: List[TestPart]
    
    def get_all_questions(self) -> List[Question]:
        """Get all questions from all parts"""
        all_questions = []
        for part in self.parts:
            all_questions.extend(part.questions)
        return all_questions
    
    def get_question_by_number(self, number: int) -> Optional[Question]:
        """Get a specific question by its number"""
        for part in self.parts:
            for question in part.questions:
                if question.number == number:
                    return question
        return None
    
    def get_total_questions(self) -> int:
        """Get the total number of questions in the test"""
        return sum(part.get_question_count() for part in self.parts)
    
    def get_part_by_number(self, part_number: int) -> Optional[TestPart]:
        """Get a specific part by its number"""
        for part in self.parts:
            if part.number == part_number:
                return part
        return None

@dataclass
class UserAnswer:
    """Represents a user's answer to a question"""
    question_number: int
    selected_choice: str  # The letter chosen (A, B, C, D)
    is_correct: Optional[bool] = None
    
    def check_correctness(self, question: Question) -> bool:
        """Check if this answer is correct"""
        if question.correct_answer:
            self.is_correct = self.selected_choice.upper() == question.correct_answer.upper()
        else:
            self.is_correct = None  # Cannot determine without answer key
        return self.is_correct if self.is_correct is not None else False

@dataclass
class TestResults:
    """Represents the results of a completed test"""
    test_name: str
    user_answers: List[UserAnswer]
    total_questions: int
    correct_answers: int
    score_percentage: float
    part_scores: dict  # {part_number: (correct, total)}
    
    @classmethod
    def calculate_results(cls, test: Test, user_answers: List[UserAnswer]) -> 'TestResults':
        """Calculate test results from user answers"""
        correct_count = 0
        part_scores = {}
        
        # Initialize part scores
        for part in test.parts:
            part_scores[part.number] = [0, 0]  # [correct, total]
        
        # Check each answer
        for answer in user_answers:
            question = test.get_question_by_number(answer.question_number)
            if question:
                part_scores[question.part][1] += 1  # Increment total for this part
                
                if answer.check_correctness(question):
                    correct_count += 1
                    part_scores[question.part][0] += 1  # Increment correct for this part
        
        total_questions = test.get_total_questions()
        score_percentage = (correct_count / total_questions * 100) if total_questions > 0 else 0
        
        return cls(
            test_name=test.name,
            user_answers=user_answers,
            total_questions=total_questions,
            correct_answers=correct_count,
            score_percentage=score_percentage,
            part_scores=part_scores
        )
