"""
Document parser for reading English test files
"""

import re
from typing import List, Optional, Tuple
from docx import Document
from question_model import Question, TestPart, Test, QuestionType

class DocumentParser:
    """Parser for .docx test files"""
    
    def __init__(self):
        self.current_part = None
        self.current_directions = ""
    
    def parse_test_file(self, filename: str) -> Optional[Test]:
        """Parse a .docx test file and return a Test object"""
        try:
            doc = Document(filename)
            test_name = filename.replace('.docx', '').replace('test-', 'Test ').title()
            
            parts = self._extract_parts(doc)
            
            if not parts:
                print(f"Warning: No parts found in {filename}")
                return None
            
            return Test(name=test_name, parts=parts)
            
        except Exception as e:
            print(f"Error parsing {filename}: {e}")
            return None
    
    def _extract_parts(self, doc: Document) -> List[TestPart]:
        """Extract all parts from the document"""
        parts = []
        current_part_info = None
        current_questions = []
        current_directions = ""
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            
            if not text:
                continue
            
            # Check for part headers
            part_match = re.match(r'^Part\s+(\d+)', text, re.IGNORECASE)
            if part_match:
                # Save previous part if exists
                if current_part_info and current_questions:
                    part = TestPart(
                        number=current_part_info,
                        title=f"Part {current_part_info}",
                        directions=current_directions,
                        questions=current_questions
                    )
                    parts.append(part)
                
                # Start new part
                current_part_info = int(part_match.group(1))
                current_questions = []
                current_directions = ""
                continue
            
            # Check for directions
            if text.lower().startswith('directions:'):
                current_directions = text
                continue
            
            # Check for questions
            question = self._parse_question(text, i, doc.paragraphs, current_part_info or 5)
            if question:
                current_questions.append(question)

            # Also check for question references (e.g., "Question 141-143 refer to...")
            question_ref_match = re.match(r'^Questions?\s+(\d+)(?:-(\d+))?\s+refer', text, re.IGNORECASE)
            if question_ref_match and current_part_info:
                # This indicates upcoming questions, continue parsing
                pass
        
        # Add the last part
        if current_part_info and current_questions:
            part = TestPart(
                number=current_part_info,
                title=f"Part {current_part_info}",
                directions=current_directions,
                questions=current_questions
            )
            parts.append(part)
        
        return parts
    
    def _parse_question(self, text: str, paragraph_index: int, all_paragraphs: List, part_number: int) -> Optional[Question]:
        """Parse a single question from text"""
        # Look for question number pattern (e.g., "101.", "102.", etc.)
        question_match = re.match(r'^(\d+)\.\s*(.*)', text)
        if not question_match:
            return None
        
        question_number = int(question_match.group(1))
        question_text = question_match.group(2).strip()
        
        # Extract choices from following paragraphs
        choices = []
        choice_pattern = re.compile(r'^\([A-D]\)\s*(.*)')
        
        # Look ahead for answer choices
        for j in range(paragraph_index + 1, min(paragraph_index + 10, len(all_paragraphs))):
            next_text = all_paragraphs[j].text.strip()
            
            if not next_text:
                continue
            
            # Check if this is a choice
            choice_match = choice_pattern.match(next_text)
            if choice_match:
                choices.append(next_text)
            else:
                # Stop if we hit another question or non-choice text
                if re.match(r'^\d+\.', next_text) or (next_text and not choice_match):
                    break
        
        # Determine question type
        question_type = QuestionType.MULTIPLE_CHOICE
        if part_number == 6:
            question_type = QuestionType.TEXT_COMPLETION
        elif part_number == 7:
            question_type = QuestionType.READING_COMPREHENSION
        
        return Question(
            number=question_number,
            text=question_text,
            choices=choices,
            part=part_number,
            question_type=question_type
        )
    
    def _extract_answer_key(self, doc: Document) -> dict:
        """Extract answer key if present in the document"""
        # This is a placeholder for answer key extraction
        # In a real implementation, you would look for patterns like:
        # "Answer Key:", "Answers:", or bold/highlighted correct answers
        answer_key = {}
        
        # Look for patterns that might indicate correct answers
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            
            # Look for bold text that might indicate correct answers
            for run in paragraph.runs:
                if run.bold and run.text.strip():
                    # This could be a correct answer
                    pass
        
        return answer_key
    
    def validate_test(self, test: Test) -> List[str]:
        """Validate the parsed test and return any issues found"""
        issues = []
        
        if not test.parts:
            issues.append("No parts found in test")
            return issues
        
        for part in test.parts:
            if not part.questions:
                issues.append(f"Part {part.number} has no questions")
                continue
            
            for question in part.questions:
                if not question.text:
                    issues.append(f"Question {question.number} has no text")
                
                if not question.choices:
                    issues.append(f"Question {question.number} has no answer choices")
                elif len(question.choices) < 2:
                    issues.append(f"Question {question.number} has fewer than 2 choices")
                
                # Check choice format
                for choice in question.choices:
                    if not re.match(r'^\([A-D]\)', choice):
                        issues.append(f"Question {question.number} has malformed choice: {choice[:30]}...")
        
        return issues

def test_parser():
    """Test the document parser"""
    parser = DocumentParser()
    
    # Test with test-2.docx (since test-1.docx seems corrupted)
    test = parser.parse_test_file("test-2.docx")
    
    if test:
        print(f"Successfully parsed: {test.name}")
        print(f"Total parts: {len(test.parts)}")
        print(f"Total questions: {test.get_total_questions()}")
        
        for part in test.parts:
            print(f"\nPart {part.number}: {part.get_question_count()} questions")
            question_range = part.get_question_range()
            print(f"Question range: {question_range[0]}-{question_range[1]}")
            
            # Show first question as example
            if part.questions:
                q = part.questions[0]
                print(f"Example question {q.number}: {q.text[:50]}...")
                for choice in q.choices[:2]:  # Show first 2 choices
                    print(f"  {choice}")
        
        # Validate the test
        issues = parser.validate_test(test)
        if issues:
            print(f"\nValidation issues found:")
            for issue in issues[:5]:  # Show first 5 issues
                print(f"  - {issue}")
        else:
            print("\nTest validation passed!")
    else:
        print("Failed to parse test file")

if __name__ == "__main__":
    test_parser()
