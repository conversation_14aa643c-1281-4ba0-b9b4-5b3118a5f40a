#!/usr/bin/env python3
"""
English Test Application
A GUI application for administering English tests from .docx files
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from document_parser import DocumentParser
from test_interface import TestInterface
from question_model import Test, TestResults

class EnglishTestApp:
    """Main application class"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.parser = DocumentParser()
        self.current_test = None
        self.test_interface = None
        
        self.setup_main_window()
        self.create_main_menu()
    
    def setup_main_window(self):
        """Set up the main application window"""
        self.root.title("English Test Application")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"800x600+{x}+{y}")
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def create_main_menu(self):
        """Create the main menu interface"""
        # Clear the window
        for widget in self.root.winfo_children():
            widget.destroy()
        
        self.root.title("English Test Application")
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="English Test Application", 
                               font=("Arial", 24, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 30))
        
        # Subtitle
        subtitle_label = ttk.Label(main_frame, text="Select a test to begin", 
                                  font=("Arial", 12))
        subtitle_label.grid(row=1, column=0, pady=(0, 40))
        
        # Test buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=2, column=0, pady=(0, 30))
        
        # Test 1 button
        test1_frame = ttk.LabelFrame(buttons_frame, text="Test 1", padding="20")
        test1_frame.grid(row=0, column=0, padx=(0, 20), pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        test1_info = ttk.Label(test1_frame, text="File: test-1.docx\nTOEIC Reading Test")
        test1_info.pack(pady=(0, 10))
        
        self.test1_button = ttk.Button(test1_frame, text="Start Test 1", 
                                      command=lambda: self.start_test("test-1.docx"),
                                      style="Accent.TButton")
        self.test1_button.pack()
        
        # Test 2 button
        test2_frame = ttk.LabelFrame(buttons_frame, text="Test 2", padding="20")
        test2_frame.grid(row=0, column=1, padx=(20, 0), pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        test2_info = ttk.Label(test2_frame, text="File: test-2.docx\nTOEIC Reading Test")
        test2_info.pack(pady=(0, 10))
        
        self.test2_button = ttk.Button(test2_frame, text="Start Test 2", 
                                      command=lambda: self.start_test("test-2.docx"),
                                      style="Accent.TButton")
        self.test2_button.pack()
        
        # Status frame
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=3, column=0, pady=(20, 0), sticky=(tk.W, tk.E))
        
        # Check file availability
        self.check_test_files()
        
        # Instructions
        instructions = ttk.Label(main_frame, 
                                text="Instructions:\n"
                                     "• Select a test to begin\n"
                                     "• Navigate through questions using Next/Previous buttons\n"
                                     "• Use the dropdown to jump to specific questions\n"
                                     "• Select your answer choice for each question\n"
                                     "• Submit the test when complete",
                                justify=tk.LEFT,
                                font=("Arial", 10))
        instructions.grid(row=4, column=0, pady=(30, 0), sticky=tk.W)
        
        # Footer
        footer = ttk.Label(main_frame, 
                          text="English Test Application v1.0",
                          font=("Arial", 8),
                          foreground="gray")
        footer.grid(row=5, column=0, pady=(30, 0))
    
    def check_test_files(self):
        """Check if test files exist and update button states"""
        test1_exists = os.path.exists("test-1.docx")
        test2_exists = os.path.exists("test-2.docx")
        
        if not test1_exists:
            self.test1_button.config(state=tk.DISABLED)
            self.test1_button.config(text="Test 1 (File Not Found)")
        
        if not test2_exists:
            self.test2_button.config(state=tk.DISABLED)
            self.test2_button.config(text="Test 2 (File Not Found)")
        
        if not test1_exists and not test2_exists:
            error_label = ttk.Label(self.root, 
                                   text="Error: No test files found!\n"
                                        "Please ensure test-1.docx and/or test-2.docx are in the application directory.",
                                   foreground="red",
                                   font=("Arial", 10, "bold"))
            error_label.grid(row=1, column=0, pady=20)
    
    def start_test(self, filename: str):
        """Start a test from the specified file"""
        if not os.path.exists(filename):
            messagebox.showerror("File Not Found", 
                               f"The test file '{filename}' was not found.\n"
                               f"Please ensure the file is in the application directory.")
            return
        
        # Show loading message
        loading_label = ttk.Label(self.root, text="Loading test...", font=("Arial", 12))
        loading_label.grid(row=1, column=0, pady=20)
        self.root.update()
        
        try:
            # Parse the test file
            test = self.parser.parse_test_file(filename)
            
            if not test:
                loading_label.destroy()
                messagebox.showerror("Parse Error", 
                                   f"Failed to parse the test file '{filename}'.\n"
                                   f"Please check that the file is a valid Word document.")
                return
            
            # Validate the test
            issues = self.parser.validate_test(test)
            if issues:
                loading_label.destroy()
                issue_text = "\\n".join(issues[:5])  # Show first 5 issues
                if len(issues) > 5:
                    issue_text += f"\\n... and {len(issues) - 5} more issues"
                
                result = messagebox.askyesno("Test Validation Issues", 
                                           f"The following issues were found in the test:\\n\\n{issue_text}\\n\\n"
                                           f"Do you want to continue anyway?")
                if not result:
                    return
            
            loading_label.destroy()
            
            # Start the test interface
            self.current_test = test
            self.test_interface = TestInterface(self.root, test, self.on_test_complete)
            
        except Exception as e:
            loading_label.destroy()
            messagebox.showerror("Error", f"An error occurred while loading the test:\\n{str(e)}")
    
    def on_test_complete(self, results: TestResults = None):
        """Handle test completion"""
        if results:
            # Test was completed, results were already shown in TestInterface
            pass
        
        # Return to main menu
        self.current_test = None
        self.test_interface = None
        self.create_main_menu()
    
    def run(self):
        """Run the application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()

def main():
    """Main entry point"""
    # Check if we're in the right directory
    if not (os.path.exists("test-1.docx") or os.path.exists("test-2.docx")):
        print("Warning: No test files found in current directory.")
        print("Please ensure test-1.docx and/or test-2.docx are in the same directory as this script.")
    
    # Create and run the application
    app = EnglishTestApp()
    app.run()

if __name__ == "__main__":
    main()
