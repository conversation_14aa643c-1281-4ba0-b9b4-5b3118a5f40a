#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to examine the structure of the test documents
"""

try:
    from docx import Document
    print("python-docx is available")
except ImportError:
    print("python-docx not available. Installing...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-docx"])
    from docx import Document

def examine_document(filename):
    """Examine the structure of a Word document"""
    print(f"\n=== Examining {filename} ===")
    try:
        doc = Document(filename)
        print(f"Number of paragraphs: {len(doc.paragraphs)}")

        # Look for parts and questions
        parts = []
        questions = []

        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if text:
                # Check for parts
                if text.startswith("Part "):
                    parts.append((i, text))
                    print(f"Found part at para {i+1}: {text}")

                # Check for questions (numbered)
                if text and text[0].isdigit() and '.' in text[:5]:
                    questions.append((i, text[:50]))
                    if len(questions) <= 10:  # Show first 10 questions
                        print(f"Question at para {i+1}: {text[:50]}...")

        print(f"\nFound {len(parts)} parts and {len(questions)} questions")

        # Show structure around parts
        for part_idx, part_text in parts:
            print(f"\n--- {part_text} ---")
            start = max(0, part_idx - 2)
            end = min(len(doc.paragraphs), part_idx + 10)
            for j in range(start, end):
                para_text = doc.paragraphs[j].text.strip()
                if para_text:
                    marker = ">>> " if j == part_idx else "    "
                    print(f"{marker}Para {j+1}: {para_text[:80]}{'...' if len(para_text) > 80 else ''}")

    except Exception as e:
        print(f"Error reading {filename}: {e}")

if __name__ == "__main__":
    examine_document("test-1.docx")
    examine_document("test-2.docx")
