"""
Test interface for the English Test Application
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from typing import Dict, List, Optional
from question_model import Test, Question, UserAnswer, TestResults

class TestInterface:
    """GUI interface for taking a test"""
    
    def __init__(self, parent, test: Test, on_complete_callback=None):
        self.parent = parent
        self.test = test
        self.on_complete_callback = on_complete_callback
        
        # Test state
        self.current_question_index = 0
        self.user_answers: Dict[int, str] = {}  # question_number -> selected_choice
        self.all_questions = test.get_all_questions()
        
        # Create the interface
        self.setup_interface()
        self.display_question()
    
    def setup_interface(self):
        """Set up the test interface"""
        # Clear parent window
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        self.parent.title(f"English Test - {self.test.name}")
        
        # Main frame
        main_frame = ttk.Frame(self.parent, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.parent.columnconfigure(0, weight=1)
        self.parent.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Progress frame
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.progress_label = ttk.Label(progress_frame, text="")
        self.progress_label.pack(side=tk.LEFT)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        
        # Question navigation frame
        nav_frame = ttk.Frame(main_frame)
        nav_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(nav_frame, text="Go to question:").pack(side=tk.LEFT)
        
        self.question_var = tk.StringVar()
        self.question_combo = ttk.Combobox(nav_frame, textvariable=self.question_var, 
                                          values=[str(q.number) for q in self.all_questions],
                                          width=10, state="readonly")
        self.question_combo.pack(side=tk.LEFT, padx=(5, 10))
        self.question_combo.bind('<<ComboboxSelected>>', self.on_question_selected)
        
        ttk.Button(nav_frame, text="Return to Main Menu", 
                  command=self.return_to_main).pack(side=tk.RIGHT)
        
        # Question display frame
        question_frame = ttk.LabelFrame(main_frame, text="Question", padding="10")
        question_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        question_frame.columnconfigure(0, weight=1)
        question_frame.rowconfigure(0, weight=1)
        
        # Question text (scrollable)
        self.question_text = scrolledtext.ScrolledText(question_frame, height=8, wrap=tk.WORD)
        self.question_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Answer choices frame
        choices_frame = ttk.LabelFrame(main_frame, text="Answer Choices", padding="10")
        choices_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.choice_var = tk.StringVar()
        self.choice_buttons = []
        
        for i in range(4):  # Maximum 4 choices (A, B, C, D)
            btn = ttk.Radiobutton(choices_frame, text="", variable=self.choice_var, 
                                 value="", command=self.on_choice_selected)
            btn.grid(row=i, column=0, sticky=tk.W, pady=2)
            self.choice_buttons.append(btn)
        
        # Navigation buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E))
        
        self.prev_button = ttk.Button(button_frame, text="← Previous", command=self.previous_question)
        self.prev_button.pack(side=tk.LEFT)
        
        self.next_button = ttk.Button(button_frame, text="Next →", command=self.next_question)
        self.next_button.pack(side=tk.LEFT, padx=(10, 0))
        
        self.submit_button = ttk.Button(button_frame, text="Submit Test", command=self.submit_test)
        self.submit_button.pack(side=tk.RIGHT)
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="")
        self.status_label.grid(row=5, column=0, columnspan=3, pady=(10, 0))
    
    def display_question(self):
        """Display the current question"""
        if not self.all_questions or self.current_question_index >= len(self.all_questions):
            return

        question = self.all_questions[self.current_question_index]
        
        # Update progress
        progress_text = f"Question {self.current_question_index + 1} of {len(self.all_questions)} (Part {question.part})"
        self.progress_label.config(text=progress_text)
        self.progress_bar['maximum'] = len(self.all_questions)
        self.progress_bar['value'] = self.current_question_index + 1
        
        # Update question selector
        self.question_var.set(str(question.number))
        
        # Display question text
        self.question_text.config(state=tk.NORMAL)
        self.question_text.delete(1.0, tk.END)
        question_display = f"Question {question.number}: {question.text}"
        self.question_text.insert(1.0, question_display)
        self.question_text.config(state=tk.DISABLED)
        
        # Display answer choices
        for i, btn in enumerate(self.choice_buttons):
            if i < len(question.choices):
                choice_text = question.choices[i]
                choice_letter = question.get_choice_letter(choice_text)
                btn.config(text=choice_text, value=choice_letter, state=tk.NORMAL)
                btn.grid()
            else:
                btn.config(text="", value="", state=tk.DISABLED)
                btn.grid_remove()
        
        # Restore previous answer if exists
        if question.number in self.user_answers:
            self.choice_var.set(self.user_answers[question.number])
        else:
            self.choice_var.set("")
        
        # Update navigation buttons
        self.prev_button.config(state=tk.NORMAL if self.current_question_index > 0 else tk.DISABLED)
        self.next_button.config(state=tk.NORMAL if self.current_question_index < len(self.all_questions) - 1 else tk.DISABLED)
        
        # Update status
        answered_count = len(self.user_answers)
        self.status_label.config(text=f"Answered: {answered_count}/{len(self.all_questions)} questions")
    
    def on_choice_selected(self):
        """Handle answer choice selection"""
        if self.all_questions and self.current_question_index < len(self.all_questions):
            question = self.all_questions[self.current_question_index]
            selected_choice = self.choice_var.get()
            if selected_choice:
                self.user_answers[question.number] = selected_choice
                self.display_question()  # Refresh to update status
    
    def previous_question(self):
        """Go to previous question"""
        if self.current_question_index > 0:
            self.current_question_index -= 1
            self.display_question()

    def next_question(self):
        """Go to next question"""
        if self.current_question_index < len(self.all_questions) - 1:
            self.current_question_index += 1
            self.display_question()
    
    def on_question_selected(self, event=None):
        """Handle question selection from dropdown"""
        try:
            selected_number = int(self.question_var.get())
            for i, question in enumerate(self.all_questions):
                if question.number == selected_number:
                    self.current_question_index = i
                    self.display_question()
                    break
        except ValueError:
            pass
    
    def submit_test(self):
        """Submit the test and show results"""
        answered_count = len(self.user_answers)
        total_count = len(self.all_questions)
        
        if answered_count < total_count:
            result = messagebox.askyesno(
                "Incomplete Test",
                f"You have answered {answered_count} out of {total_count} questions.\n"
                f"Do you want to submit the test anyway?"
            )
            if not result:
                return
        
        # Convert user answers to UserAnswer objects
        user_answer_objects = []
        for question_number, selected_choice in self.user_answers.items():
            user_answer_objects.append(UserAnswer(question_number, selected_choice))
        
        # Calculate results (Note: without answer key, we can't determine correctness)
        results = TestResults(
            test_name=self.test.name,
            user_answers=user_answer_objects,
            total_questions=total_count,
            correct_answers=0,  # Cannot determine without answer key
            score_percentage=0.0,  # Cannot determine without answer key
            part_scores={}
        )
        
        self.show_results(results)
    
    def show_results(self, results: TestResults):
        """Display test results"""
        result_text = f"Test Completed: {results.test_name}\n\n"
        result_text += f"Questions Answered: {len(results.user_answers)}/{results.total_questions}\n"
        result_text += f"Completion Rate: {len(results.user_answers)/results.total_questions*100:.1f}%\n\n"
        result_text += "Note: Scoring requires an answer key which is not available in the current test files.\n"
        result_text += "Your answers have been recorded for review."
        
        messagebox.showinfo("Test Results", result_text)
        
        if self.on_complete_callback:
            self.on_complete_callback(results)
    
    def return_to_main(self):
        """Return to main menu"""
        result = messagebox.askyesno(
            "Return to Main Menu",
            "Are you sure you want to return to the main menu?\nYour progress will be lost."
        )
        if result and self.on_complete_callback:
            self.on_complete_callback(None)
