# English Test Application

A Python GUI application for administering English tests (specifically TOEIC Reading tests) from Microsoft Word (.docx) files.

## Features

### Main Application
- **Clean, professional GUI** with main menu and test selection
- **Two test options**: Start Test 1 (test-1.docx) and Start Test 2 (test-2.docx)
- **Error handling** for missing or corrupted files
- **File validation** with user-friendly error messages

### Test Interface
- **Question-by-question display** with clear, readable formatting
- **Multiple choice answer selection** using radio buttons
- **Navigation system**:
  - Next/Previous buttons for sequential navigation
  - Question dropdown selector to jump to specific questions
  - Progress indicator showing "Question X of Y" and current part
- **Answer tracking**: Visual indication of answered vs. unanswered questions
- **Return to Main Menu** option with confirmation

### Test Structure Support
- **Part 5**: Vocabulary and grammar questions (101-140)
- **Part 6**: Text completion questions (141-152) 
- **Part 7**: Reading comprehension questions (153-200)
- **Automatic part detection** and question categorization

### Test Management
- **Submit Test** button with confirmation dialog
- **Answer validation**: Warns if not all questions are answered
- **Progress tracking**: Shows completion percentage
- **Results display**: Summary of answered questions and completion rate

## Installation

### Prerequisites
- Python 3.8 or higher
- tkinter (usually included with Python)

### Setup
1. Clone or download this repository
2. Navigate to the project directory
3. Create a virtual environment (recommended):
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\\Scripts\\activate
   ```
4. Install required dependencies:
   ```bash
   pip install python-docx
   ```

### Test Files
Place your test files in the same directory as the application:
- `test-1.docx` - First test file
- `test-2.docx` - Second test file

## Usage

### Running the Application
```bash
python3 english_test_app.py
```

### Taking a Test
1. **Start**: Click "Start Test 1" or "Start Test 2" from the main menu
2. **Navigate**: Use Next/Previous buttons or the question dropdown
3. **Answer**: Select your choice using the radio buttons (A, B, C, D)
4. **Track Progress**: Monitor your progress with the progress bar and status
5. **Submit**: Click "Submit Test" when complete

### Navigation Features
- **Sequential Navigation**: Use Next/Previous buttons
- **Direct Navigation**: Select any question number from the dropdown
- **Progress Tracking**: See current question, total questions, and part number
- **Answer Status**: Track how many questions you've answered

## File Structure

```
english_test_app/
├── english_test_app.py      # Main application entry point
├── test_interface.py        # Test taking GUI interface
├── document_parser.py       # .docx file parsing functionality
├── question_model.py        # Data structures for questions and tests
├── test-1.docx             # Test file 1 (user provided)
├── test-2.docx             # Test file 2 (user provided)
└── README.md               # This file
```

## Technical Details

### Document Format Requirements
The application expects .docx files with the following structure:
- **Part headers**: "Part 5", "Part 6", "Part 7"
- **Question format**: "101. Question text here"
- **Answer choices**: "(A) Choice text", "(B) Choice text", etc.
- **Sequential numbering**: Questions should be numbered sequentially

### Supported Question Types
- **Multiple Choice**: Standard A/B/C/D format
- **Text Completion**: Fill-in-the-blank with multiple choice options
- **Reading Comprehension**: Questions based on reading passages

### Error Handling
- **File not found**: Clear error messages for missing test files
- **Parse errors**: Validation of document structure with detailed error reporting
- **Incomplete tests**: Warning when submitting with unanswered questions
- **Navigation errors**: Graceful handling of invalid question selections

## Limitations

### Current Limitations
1. **Answer Key**: The application does not currently support automatic scoring as it cannot detect correct answers from the .docx files. This would require:
   - A separate answer key file, or
   - Specific formatting in the .docx file to indicate correct answers (e.g., bold text)

2. **Test File Format**: Currently only supports .docx files with specific formatting
3. **Image Support**: Does not display images from the .docx files
4. **Timer**: No built-in timer functionality

### Future Enhancements
- **Answer key support** for automatic scoring
- **Timer functionality** with countdown display
- **Results export** to PDF or CSV
- **User profiles** and score history
- **Review mode** showing correct answers
- **Image display** for questions with diagrams

## Troubleshooting

### Common Issues

**"File not found" error**:
- Ensure test-1.docx and/or test-2.docx are in the same directory as the Python script
- Check file permissions

**"Parse error" when loading test**:
- Verify the .docx file is not corrupted
- Check that the file follows the expected format (Part headers, numbered questions, etc.)
- Try opening the file in Microsoft Word to verify it's valid

**GUI not appearing**:
- Ensure tkinter is installed (usually comes with Python)
- On Linux, you may need to install: `sudo apt-get install python3-tk`

**Import errors**:
- Ensure you're running from the correct directory
- Activate the virtual environment if using one
- Install python-docx: `pip install python-docx`

## Development

### Testing the Parser
To test the document parser independently:
```bash
python3 document_parser.py
```

### Code Structure
- **Object-oriented design** with separate classes for different functionality
- **Model-View separation** between data structures and GUI
- **Error handling** throughout the application
- **Type hints** for better code documentation

## License

This project is provided as-is for educational and testing purposes.
